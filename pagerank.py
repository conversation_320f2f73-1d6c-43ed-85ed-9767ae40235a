import numpy as np
import time
from scipy.sparse import identity, csc_matrix
from scipy.sparse.linalg import spsolve
from visualizations import create_visualizations
import time
start_time = time.time()

def format_results(arr, G, index_to_npi):
    in_degrees = np.array(G.sum(axis=0)).flatten()
    out_degrees = np.array(G.sum(axis=1)).flatten()

    results = []
    for i, pr_value in enumerate(arr):
        results.append({
            'node_id': index_to_npi[i],
            'pagerank': pr_value,
            'in_degree': int(in_degrees[i]),
            'out_degree': int(out_degrees[i])
        })

    results.sort(key=lambda x: x['pagerank'], reverse=True)
    return [f"NPI {r['node_id']}: PR = {r['pagerank']:.4f}, In = {r['in_degree']}, Out = {r['out_degree']}"
            for r in results]

def print_pagerank_results(results):
    for result in results:
        print(result)

def pagerank1(G, p=0.85):
    n = G.shape[0]
    c = G.sum(axis=0).A[0]
    D = identity(n, format='csc')
    D.setdiag(1 / np.where(c != 0, c, 1))
    e = np.ones(n) / n
    I = identity(n, format='csc')
    x = spsolve(I - p * G @ D, e)
    return x / np.sum(x)

def pagerank2(G, p=0.85, tol=1e-8, max_iter=100):
    n = G.shape[0]
    c = G.sum(axis=0).A[0]
    D = identity(n, format='csc')
    D.setdiag(1 / np.where(c != 0, c, 1))
    delta = (1 - p) / n * np.ones(n)
    x = np.ones(n) / n

    for _ in range(max_iter):
        x_old = x.copy()
        x = p * (G @ D @ x) + delta * np.sum(x)
        x = x / np.sum(x)
        if np.linalg.norm(x - x_old, 1) < tol:
            break
    return x

def pagerank3(G, p=0.85, tol=1e-8, max_iter=100):
    n = G.shape[0]
    c = G.sum(axis=0).A[0]
    D = identity(n, format='csc')
    D.setdiag(1 / np.where(c != 0, c, 1))
    e = np.ones(n) / n
    x = e.copy()
    z = np.ones(n) / n
    z[np.where(c != 0)[0]] -= p / n

    for _ in range(max_iter):
        x_old = x.copy()
        x = (p * (G @ D)) @ x + e * (z @ x)
        if np.linalg.norm(x - x_old, 1) < tol:
            break
    return x / np.sum(x)

def load_graph(filename):
    print(f"[Checkpoint] Loading file: {filename}")
    edges_raw = np.loadtxt(filename, delimiter=',', dtype=np.int64)
    print("[Checkpoint] File loaded successfully")

    edges = edges_raw[:, :2]
    unique_npis = np.unique(edges)
    print(f"[Checkpoint] Found {len(unique_npis)} unique NPIs")

    npi_to_index = {npi: idx for idx, npi in enumerate(unique_npis)}
    index_to_npi = {idx: npi for npi, idx in npi_to_index.items()}

    print("[Checkpoint] Mapping NPIs to indices")

    edges_indexed = np.array([[npi_to_index[src], npi_to_index[dst]] for src, dst in edges])
    print("[Checkpoint] Edges relabeled")

    n = len(unique_npis)
    G = csc_matrix((np.ones(len(edges_indexed)), (edges_indexed[:, 0], edges_indexed[:, 1])), shape=(n, n))

    print("[Checkpoint] Graph construction complete")
    return G, index_to_npi


def analyze_graph(G, index_to_npi, sizes=[100, 1000, 10000, None]):
    results = {}
    computation_times = {'pr1': [], 'pr2': [], 'pr3': []}

    for size in sizes:
        actual_size = G.shape[0] if size is None else min(size, G.shape[0])
        G_sub = G[:actual_size, :actual_size]
        size_label = "Full" if size is None else str(size)

        print(f"\n[Checkpoint] Analyzing graph of size {size_label}")
        print(f"  Nodes: {G_sub.shape[0]}, Edges: {G_sub.nnz}")
        
        results[size_label] = {'stats': {'nodes': G_sub.shape[0], 'edges': G_sub.nnz}}
        pr_values_dict = {}

        for method_name, method_func in [
            ('pr1', pagerank1),
            ('pr2', pagerank2),
            ('pr3', pagerank3)
        ]:
            if method_name == 'pr1' and size_label == 'Full':
                print("[Checkpoint] Skipping pr1 for full size due to resource constraints")
                continue
            print(f"[Checkpoint] Running {method_name} on size {size_label}...")
            start_time = time.time()
            pr_values = method_func(G_sub)
            elapsed = time.time() - start_time
            print(f"  → Completed in {elapsed:.2f} seconds")

            computation_times[method_name].append(elapsed)
            pr_values_dict[method_name] = pr_values

            print(f"[Checkpoint] Formatting results for {method_name}")
            results[size_label][method_name] = format_results(pr_values, G_sub, index_to_npi)

            print(f"[Checkpoint] Top 5 results for {method_name}:")
            print_pagerank_results(results[size_label][method_name][:5])

        print(f"[Checkpoint] Creating visualizations for size {size_label}")
        create_visualizations(G_sub, pr_values_dict, actual_size)

    print("[Checkpoint] All analyses complete")
    return results

if __name__ == "__main__":
    try:
        print("[Checkpoint] Starting PageRank analysis...")
        G, index_to_npi = load_graph('physician-shared-patient-patterns-2015-days180.txt')
        print(f"[Checkpoint] Loaded graph with {G.shape[0]} nodes and {G.nnz} edges")
        analyze_graph(G, index_to_npi)
    except FileNotFoundError:
        print("Dataset not found.")

print(f"\n[Checkpoint] Total runtime: {time.time() - start_time:.2f} seconds")